import React, { useState, useRef, useCallback, useMemo, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  ConditionalField,
  Typography,
  Chip,
  IconCard,
  CollapsibleCard,
  FormMultiWrapper,
} from '@/shared/components/common';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';
import { Controller } from 'react-hook-form';
import { ConditionType } from '@/shared/hooks/useFieldCondition';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  CreateProductResponse,
  ProductDto,

  ProductTypeEnum,
  ShipmentConfigDto,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { ProductService } from '../../services/product.service';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import CustomFieldRenderer from '../CustomFieldRenderer';
import SimpleCustomFieldSelector from '../SimpleCustomFieldSelector';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';

// Interface cho response từ backend khi có ảnh
interface ProductWithImagesResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

interface ProductWithUploadUrlsResponse {
  id: string;
  name: string;
  price: HasPriceDto | StringPriceDto | null;
  typePrice: string;
  description?: string;
  images: Array<{
    key: string;
    position: number;
    url: string;
  }>;
  uploadUrls: {
    productId: string;
    imagesUploadUrls: Array<{
      url: string;
      key: string;
      index: number;
    }>;
  };
}

interface ComboProductFormProps {
  onSubmit: (
    values: CreateProductDto
  ) => Promise<
    CreateProductResponse | ProductDto | ProductWithImagesResponse | ProductWithUploadUrlsResponse
  >;
  onCancel: () => void;
  isSubmitting: boolean;
}

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number;
  fieldId: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  value: Record<string, unknown>;
}

// Interface cho combo product item
interface ComboProductItem {
  productId: number;
  productName: string;
  quantity: number;
  discountPercent?: number;
}

// Interface cho form values
interface ComboProductFormValues {
  name: string;
  typePrice: PriceTypeEnum;
  listPrice?: string | number;
  salePrice?: string | number;
  currency?: string;
  priceDescription?: string;
  description?: string;
  tags?: string[];
  customFields?: SelectedCustomField[];
  media?: FileWithMetadata[];
  // Shipping configuration (giữ nguyên từ ProductForm)
  shipmentConfig?: ShipmentConfigDto;
  // Inventory configuration
  inventory?: {
    warehouseId?: string | number;
    availableQuantity?: string | number;
    sku?: string;
    barcode?: string;
  };
  // Combo product specific fields
  comboProducts: ComboProductItem[];
}

/**
 * Form tạo combo sản phẩm
 */
const ComboProductForm: React.FC<ComboProductFormProps> = ({ onSubmit, onCancel, isSubmitting }) => {
  const { t } = useTranslation(['business', 'common']);

  // Schema validation cho combo sản phẩm
  const comboProductSchema = z
    .object({
      name: z.string().min(1, 'Tên combo không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      shipmentConfig: z.any().optional(),
      // Inventory validation
      inventory: z
        .object({
          warehouseId: z.union([z.string(), z.number()]).optional(),
          availableQuantity: z.union([z.string(), z.number()]).optional(),
          sku: z.string().optional(),
          barcode: z.string().optional(),
        })
        .optional(),
      // Combo product specific validations
      comboProducts: z
        .array(
          z.object({
            productId: z.number().min(1, 'ID sản phẩm không hợp lệ'),
            productName: z.string().min(1, 'Tên sản phẩm không được để trống'),
            quantity: z.number().min(1, 'Số lượng phải lớn hơn 0'),
            discountPercent: z.number().min(0).max(100).optional(),
          })
        )
        .min(1, 'Combo phải có ít nhất 1 sản phẩm'),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        // Giá bán là bắt buộc
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        // Đơn vị tiền tệ là bắt buộc
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
        // Không cần validate giá niêm yết vì nó được tính tự động
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho combo products
  const [comboProducts, setComboProducts] = useState<ComboProductItem[]>([]);

  // State cho loaded products (để cache)
  const [loadedProducts, setLoadedProducts] = useState<Map<string, ProductDto>>(new Map());

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    console.log('🚀 ComboProductForm handleSubmit called with values:', values);

    if (!values.name || !values.typePrice) {
      console.error('❌ Missing required fields:', {
        name: values.name,
        typePrice: values.typePrice,
      });
      NotificationUtil.error({
        message: 'Vui lòng nhập tên combo và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    if (comboProducts.length === 0) {
      NotificationUtil.error({
        message: 'Combo phải có ít nhất 1 sản phẩm',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as ComboProductFormValues;
      setIsUploading(true);

      console.log('✅ Form values before processing:', formValues);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        console.error('❌ Price validation error:', priceError);
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo advancedInfo cho combo sản phẩm theo API structure
      const advancedInfo = {
        purchaseCount: 0,
        info: comboProducts.map(item => ({
          productId: item.productId,
          total: item.quantity,
        })),
      };

      // Tạo inventory data từ form hoặc giá trị mặc định
      const inventoryData = {
        warehouseId: formValues.inventory?.warehouseId ? Number(formValues.inventory.warehouseId) : 1,
        availableQuantity: formValues.inventory?.availableQuantity ? Number(formValues.inventory.availableQuantity) : 50,
        sku: formValues.inventory?.sku || `COMBO-${Date.now()}`,
        barcode: formValues.inventory?.barcode || `${Date.now()}${Math.floor(Math.random() * 1000)}`,
      };

      const productData: CreateProductDto = {
        name: formValues.name,
        productType: ProductTypeEnum.COMBO,
        typePrice: formValues.typePrice,
        price: priceData,
        description: formValues.description || undefined,
        tags: formValues.tags && formValues.tags.length > 0 ? formValues.tags : undefined,
        imagesMediaTypes:
          mediaFiles.length > 0 ? mediaFiles.map(file => file.file.type) : undefined,
        customFields:
          productCustomFields.length > 0
            ? productCustomFields
                .filter(field => {
                  // Lọc ra những field có giá trị không rỗng
                  const fieldValue = field.value?.value;
                  return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
                })
                .map(field => ({
                  customFieldId: field.fieldId,
                  value: field.value,
                }))
            : undefined,
        inventory: inventoryData,
        advancedInfo,
      };

      console.log('🔍 ComboProductForm - Final data structure:');
      console.log('📋 Combo Products:', comboProducts);
      console.log('💰 Price Data:', priceData);
      console.log('📦 Inventory Data:', inventoryData);
      console.log('🔧 Advanced Info:', advancedInfo);
      console.log('📤 Final combo product data to be sent to API:', JSON.stringify(productData, null, 2));

      // Gọi callback onSubmit để parent component xử lý API call và nhận response
      const response = await onSubmit(productData);

      console.log('✅ Combo product created successfully:', response);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index];
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo.url,
                  key: uploadInfo.key,
                  index: uploadInfo.index,
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            console.warn('⚠️ Media files exist but no upload URLs provided from backend');
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch (uploadError) {
          console.error('❌ Error uploading combo product images:', uploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      setIsUploading(false);
    } catch (error) {
      console.error('Error in ComboProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: ComboProductFormValues): HasPriceDto | StringPriceDto | null => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }
      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const salePrice = Number(values.salePrice);

      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }

      // Tính giá niêm yết từ tổng giá sản phẩm trong combo
      const listPrice = calculateComboListPrice();

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Hàm lấy giá sản phẩm
  const getProductPrice = useCallback((product: ProductDto): number => {
    if (product.typePrice === PriceTypeEnum.HAS_PRICE && product.price) {
      const price = product.price as HasPriceDto;
      return price.salePrice || price.listPrice || 0;
    }
    return 0;
  }, []);

  // Hàm tính tổng giá niêm yết của combo
  const calculateComboListPrice = useCallback((): number => {
    return comboProducts.reduce((total, item) => {
      const productData = loadedProducts.get(item.productId.toString());
      const originalPrice = productData ? getProductPrice(productData) : 0;
      return total + (originalPrice * item.quantity);
    }, 0);
  }, [comboProducts, loadedProducts, getProductPrice]);

  // Load sản phẩm cho AsyncSelectWithPagination
  const loadProducts = useCallback(async ({ search, page, limit }: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await ProductService.getProducts({
        search: search || '',
        page: page || 1,
        limit: limit || 20,
      });

      // Cache products
      const newProducts = new Map(loadedProducts);
      response.items.forEach((product: ProductDto) => {
        newProducts.set(product.id.toString(), product);
      });
      setLoadedProducts(newProducts);

      return {
        items: response.items.map((product: ProductDto) => ({
          value: product.id.toString(),
          label: product.name || 'N/A',
          subtitle: `${getProductPrice(product).toLocaleString('vi-VN')} VND`,
          data: product as unknown as Record<string, unknown>,
        })),
        totalItems: response.meta.totalItems,
        totalPages: response.meta.totalPages,
        currentPage: response.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading products:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  }, [loadedProducts, getProductPrice]);

  // Thêm sản phẩm vào combo
  const handleAddComboProduct = useCallback((productId: number, productName: string) => {
    const existingProduct = comboProducts.find(item => item.productId === productId);
    if (existingProduct) {
      NotificationUtil.warning({
        message: 'Sản phẩm này đã có trong combo',
        duration: 3000,
      });
      return;
    }

    const newComboProduct: ComboProductItem = {
      productId,
      productName,
      quantity: 1,
      discountPercent: 0,
    };

    setComboProducts(prev => [...prev, newComboProduct]);
  }, [comboProducts]);

  // Xử lý chọn sản phẩm từ AsyncSelectWithPagination
  const handleProductSelect = useCallback((value: string | number | string[] | number[] | undefined) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      return;
    }

    // Get the first value if it's an array
    const productId = Array.isArray(value) ? value[0] : value;
    const productIdStr = productId.toString();

    // Get product data from loaded products
    const productData = loadedProducts.get(productIdStr);

    if (productData) {
      handleAddComboProduct(Number(productData.id), productData.name);
    }
  }, [loadedProducts, handleAddComboProduct]);

  // Xóa sản phẩm khỏi combo
  const handleRemoveComboProduct = useCallback((productId: number) => {
    setComboProducts(prev => prev.filter(item => item.productId !== productId));
  }, []);

  // Cập nhật số lượng sản phẩm trong combo
  const handleUpdateComboProductQuantity = useCallback((productId: number, quantity: number) => {
    if (quantity < 1) return;
    setComboProducts(prev =>
      prev.map(item =>
        item.productId === productId ? { ...item, quantity } : item
      )
    );
  }, []);

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.label as string) || `Field ${fieldId}`,
          component: (fieldData?.component as string) || (fieldData?.type as string) || 'text',
          type: (fieldData?.type as string) || 'text',
          required: (fieldData?.required as boolean) || false,
          configJson: (fieldData?.configJson as Record<string, unknown>) || {},
          value: { value: '' },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      shipmentConfig: undefined,
      // Inventory defaults
      inventory: {
        warehouseId: 1,
        availableQuantity: 50,
        sku: '',
        barcode: '',
      },
      // Combo product defaults
      comboProducts: [],
    }),
    []
  );

  // Đồng bộ comboProducts với form khi state thay đổi
  useEffect(() => {
    if (formRef.current) {
      formRef.current.setValues({ comboProducts });
    }
  }, [comboProducts]);

  return (
    <FormMultiWrapper title={t('business:product.form.createComboTitle', 'Tạo combo sản phẩm')}>
      <Form
        ref={formRef}
        schema={comboProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.generalInfo', '1. Thông tin chung')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="name" label={t('business:product.name')} required>
              <Input fullWidth placeholder="Nhập tên combo sản phẩm" />
            </FormItem>

            <FormItem name="description" label={t('business:product.form.description')}>
              <Textarea
                fullWidth
                rows={4}
                placeholder="Mô tả chi tiết về combo sản phẩm"
              />
            </FormItem>

            <FormItem name="tags" label={t('business:product.tags')}>
              <Controller
                name="tags"
                render={({ field }) => (
                  <div className="space-y-2">
                    <Input
                      fullWidth
                      placeholder="Nhập tag và nhấn Enter"
                      onKeyDown={e => {
                        if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                          e.preventDefault();
                          const newTag = e.currentTarget.value.trim();
                          if (!tempTags.includes(newTag)) {
                            const newTags = [...tempTags, newTag];
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }
                          e.currentTarget.value = '';
                        }
                      }}
                    />
                    <div className="flex flex-wrap gap-1 mt-2">
                      {tempTags.map((tag, tagIndex) => (
                        <Chip
                          key={`tag-${tagIndex}-${tag}`}
                          size="sm"
                          closable
                          onClose={() => {
                            const newTags = tempTags.filter(t => t !== tag);
                            setTempTags(newTags);
                            field.onChange(newTags);
                          }}
                        >
                          {tag}
                        </Chip>
                      ))}
                    </div>
                  </div>
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 2. Sản phẩm trong combo */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.comboProducts', '2. Sản phẩm trong combo')}
            </Typography>
          }
          defaultOpen={true}
          className="mb-4"
        >
          <div className="space-y-4">
            {/* Tìm kiếm và chọn sản phẩm */}
            <div className="space-y-3">
              <Typography variant="body2" className="font-medium">
                Tìm kiếm sản phẩm để thêm vào combo:
              </Typography>
              <AsyncSelectWithPagination
                loadOptions={loadProducts}
                placeholder={t('business:order.searchProductPlaceholder', 'Tìm kiếm sản phẩm...')}
                searchOnEnter={true}
                debounceTime={300}
                itemsPerPage={20}
                noOptionsMessage={t('business:order.noProductsFound', 'Không tìm thấy sản phẩm')}
                loadingMessage={t('common:loading', 'Đang tải...')}
                onChange={handleProductSelect}
                fullWidth
              />
            </div>

            {/* Danh sách sản phẩm đã chọn */}
            {comboProducts.length > 0 && (
              <div className="space-y-4">
                <Typography variant="subtitle2" className="font-medium text-muted-foreground">
                  Sản phẩm đã chọn ({comboProducts.length}):
                </Typography>

                {/* Header */}
                <div className="grid grid-cols-10 gap-4 py-3 px-4 bg-gray-50 dark:bg-gray-800 rounded-lg font-medium text-sm">
                  <div className="col-span-5 flex items-center">Tên sản phẩm</div>
                  <div className="col-span-2 text-center flex items-center justify-center">Số lượng</div>
                  <div className="col-span-2 text-center flex items-center justify-center">Giá gốc</div>
                  <div className="col-span-1 text-center flex items-center justify-center">Thao tác</div>
                </div>

                {/* Product rows */}
                <div className="space-y-2">
                  {comboProducts.map(item => {
                    const productData = loadedProducts.get(item.productId.toString());
                    const originalPrice = productData ? getProductPrice(productData) : 0;

                    return (
                      <div key={item.productId} className="grid grid-cols-10 gap-4 py-3 px-4 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg items-center">
                        {/* Tên sản phẩm */}
                        <div className="col-span-5">
                          <div className="flex items-center gap-3">
                            {productData?.images?.[0]?.url && (
                              <img
                                src={productData.images[0].url}
                                alt={item.productName}
                                className="w-10 h-10 object-cover rounded"
                              />
                            )}
                            <Typography variant="subtitle2" className="font-medium">
                              {item.productName}
                            </Typography>
                          </div>
                        </div>

                        {/* Số lượng */}
                        <div className="col-span-2 flex justify-center items-center">
                          <div className="flex items-center gap-1">
                            <IconCard
                              icon="minus"
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUpdateComboProductQuantity(
                                item.productId,
                                Math.max(1, item.quantity - 1)
                              )}
                              className="cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
                            />
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={e => handleUpdateComboProductQuantity(
                                item.productId,
                                parseInt(e.target.value) || 1
                              )}
                              className="w-16 text-center"
                            />
                            <IconCard
                              icon="plus"
                              variant="ghost"
                              size="sm"
                              onClick={() => handleUpdateComboProductQuantity(
                                item.productId,
                                item.quantity + 1
                              )}
                              className="cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-300"
                            />
                          </div>
                        </div>

                        {/* Giá gốc */}
                        <div className="col-span-2 text-center flex items-center justify-center">
                          <Typography variant="body2">
                            {originalPrice.toLocaleString('vi-VN')}đ
                          </Typography>
                        </div>

                        {/* Thao tác */}
                        <div className="col-span-1 flex justify-center items-center">
                          <IconCard
                            icon="trash"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveComboProduct(item.productId)}
                            className="cursor-pointer text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>

                {/* Tổng cộng */}
                <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="text-right">
                    <Typography variant="subtitle1" className="font-semibold">
                      Tổng cộng: {comboProducts.reduce((total, item) => {
                        const productData = loadedProducts.get(item.productId.toString());
                        const originalPrice = productData ? getProductPrice(productData) : 0;
                        return total + (originalPrice * item.quantity);
                      }, 0).toLocaleString('vi-VN')}đ
                    </Typography>
                  </div>
                </div>
              </div>
            )}

            {comboProducts.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                <Typography variant="body2">
                  Chưa có sản phẩm nào trong combo. Hãy chọn sản phẩm ở trên.
                </Typography>
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* 3. Giá combo */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.pricing', '3. Giá combo')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="typePrice" label={t('business:product.priceType.title')} required>
              <Select
                fullWidth
                options={[
                  {
                    value: PriceTypeEnum.HAS_PRICE,
                    label: t('business:product.priceType.hasPrice'),
                  },
                  {
                    value: PriceTypeEnum.STRING_PRICE,
                    label: t('business:product.priceType.stringPrice'),
                  },
                ]}
              />
            </FormItem>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.HAS_PRICE,
              }}
            >
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <label className="block text-sm font-medium text-foreground">
                      {t('business:product.listPrice', 'Giá niêm yết')}
                    </label>
                    <Input
                      fullWidth
                      disabled
                      value={`${calculateComboListPrice().toLocaleString('vi-VN')}đ`}
                      readOnly
                    />
                    <Typography variant="caption" className="text-muted-foreground">
                      Tự động tính từ tổng giá sản phẩm trong combo
                    </Typography>
                  </div>
                  <FormItem name="salePrice" label={t('business:product.salePrice')} required>
                    <Input fullWidth type="number" min="0" placeholder="Nhập giá bán" />
                  </FormItem>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItem name="currency" label={t('business:product.currency')} required>
                    <Controller
                      name="currency"
                      render={({ field }) => (
                        <Select
                          fullWidth
                          value={field.value || 'VND'}
                          onChange={value => field.onChange(value)}
                          options={[
                            { value: 'VND', label: 'VND' },
                            { value: 'USD', label: 'USD' },
                            { value: 'EUR', label: 'EUR' },
                          ]}
                        />
                      )}
                    />
                  </FormItem>
                  <div className="flex items-end">
                    <Typography variant="body2" className="text-muted-foreground">
                      Giá bán thường thấp hơn giá niêm yết để tạo ưu đãi
                    </Typography>
                  </div>
                </div>
              </div>
            </ConditionalField>

            <ConditionalField
              condition={{
                field: 'typePrice',
                type: ConditionType.EQUALS,
                value: PriceTypeEnum.STRING_PRICE,
              }}
            >
              <FormItem
                name="priceDescription"
                label={t('business:product.priceDescription')}
                required
              >
                <Input
                  fullWidth
                  placeholder="Ví dụ: Liên hệ để báo giá combo"
                />
              </FormItem>
            </ConditionalField>
          </div>
        </CollapsibleCard>

        {/* 4. Hình ảnh combo */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.media', '4. Hình ảnh combo')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <FormItem name="media" label={t('business:product.form.media')}>
              <Controller
                name="media"
                render={({ field }) => (
                  <MultiFileUpload
                    value={mediaFiles}
                    onChange={(files: FileWithMetadata[]) => {
                      setMediaFiles(files);
                      field.onChange(files);
                    }}
                    accept="image/*"
                    mediaOnly={true}
                    placeholder={t(
                      'business:product.form.mediaPlaceholder',
                      'Kéo thả hoặc click để tải lên ảnh/video'
                    )}
                    className="w-full"
                  />
                )}
              />
            </FormItem>
          </div>
        </CollapsibleCard>

        {/* 5. Trường tùy chỉnh */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.customFields', '5. Trường tùy chỉnh')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <SimpleCustomFieldSelector
              onFieldSelect={fieldData => {
                handleToggleCustomFieldToProduct(
                  fieldData.id,
                  fieldData as unknown as Record<string, unknown>
                );
              }}
              selectedFieldIds={productCustomFields.map(field => field.fieldId)}
            />

            {productCustomFields.length > 0 && (
              <div className="space-y-4 mt-4">
                <Typography variant="body2" className="font-medium">
                  {t('business:product.form.selectedCustomFields', 'Trường tùy chỉnh đã chọn:')}
                </Typography>
                {productCustomFields.map(field => (
                  <CustomFieldRenderer
                    key={field.id}
                    field={field}
                    value={field.value as unknown as string | number | boolean}
                    onChange={(value: string | number | boolean) =>
                      handleUpdateCustomFieldInProduct(field.id, String(value))
                    }
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </CollapsibleCard>

        {/* 6. Thông tin tồn kho */}
        <CollapsibleCard
          title={
            <Typography variant="h6" className="font-medium">
              {t('business:product.form.sections.inventory', '6. Thông tin tồn kho')}
            </Typography>
          }
          defaultOpen={false}
          className="mb-4"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="inventory.warehouseId" label={t('business:product.inventory.warehouseId', 'ID Kho hàng')}>
                <Input fullWidth type="number" min="1" placeholder="Nhập ID kho hàng" />
              </FormItem>
              <FormItem name="inventory.availableQuantity" label={t('business:product.inventory.availableQuantity', 'Số lượng có sẵn')}>
                <Input fullWidth type="number" min="0" placeholder="Nhập số lượng có sẵn" />
              </FormItem>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem name="inventory.sku" label={t('business:product.inventory.sku', 'Mã SKU')}>
                <Input fullWidth placeholder="Nhập mã SKU (để trống để tự động tạo)" />
              </FormItem>
              <FormItem name="inventory.barcode" label={t('business:product.inventory.barcode', 'Mã vạch')}>
                <Input fullWidth placeholder="Nhập mã vạch (để trống để tự động tạo)" />
              </FormItem>
            </div>
          </div>
        </CollapsibleCard>

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              isSubmitting || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo combo')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={isSubmitting || isUploading || comboProducts.length === 0}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default ComboProductForm;
