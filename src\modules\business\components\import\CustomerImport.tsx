import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Card,
  Stepper,
  IconCard,
} from '@/shared/components/common';
import {
  ColumnMapping,
  ExcelData
} from '../../types/customer-import.types';
import { useCustomerImport } from '../../hooks';
import ExcelUploadStep from './steps/ExcelUploadStep';
import ColumnMappingStep from './steps/ColumnMappingStep';
import ImportPreviewStep from './steps/ImportPreviewStep';
import ImportProgressStep from './steps/ImportProgressStep';

interface CustomerImportProps {
  onClose: () => void;
  onImportComplete: (importedCount: number) => void;
}

/**
 * Component cho import khách hàng từ Excel sử dụng SlideInForm
 */
const CustomerImport: React.FC<CustomerImportProps> = ({
  onClose,
  onImportComplete,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Use customer import hook
  const {
    importState,
    updateMapping,
    startImport,
    resetImportState,
    goToStep,
    previousStep,
    updateImportState,
  } = useCustomerImport();

  // State cho tab hiện tại (file upload hoặc URL)
  const [activeTab, setActiveTab] = useState<'file' | 'url'>('file');

  // Reset state khi đóng
  const handleClose = () => {
    resetImportState();
    setActiveTab('file');
    onClose();
  };

  // Xử lý khi upload Excel thành công
  const handleExcelUploaded = (excelData: ExcelData) => {
    console.log('handleExcelUploaded called with:', excelData);
    // Cập nhật state với dữ liệu Excel đã parse và chuyển sang mapping step
    updateImportState({
      step: 'mapping',
      excelData: excelData,
    });
  };

  // Xử lý khi mapping columns hoàn thành
  const handleMappingComplete = (mappings: ColumnMapping[]) => {
    // Update mappings và chuyển sang preview
    mappings.forEach((mapping, index) => {
      updateMapping(index, mapping);
    });
    goToStep('preview');
  };

  // Xử lý khi preview hoàn thành và bắt đầu import
  const handleStartImport = () => {
    startImport();
  };

  // Xử lý khi import hoàn thành
  const handleImportComplete = (importedCount: number) => {
    goToStep('complete');
    onImportComplete(importedCount);
  };

  // Xử lý quay lại step trước
  const handleGoBack = () => {
    previousStep();
  };

  // Render step content
  const renderStepContent = () => {
    console.log('Current step:', importState.step);
    switch (importState.step) {
      case 'upload':
        return (
          <ExcelUploadStep
            activeTab={activeTab}
            onTabChange={setActiveTab}
            onExcelUploaded={handleExcelUploaded}
          />
        );
      case 'mapping':
        if (!importState.excelData) return null;
        return (
          <ColumnMappingStep
            excelData={importState.excelData}
            existingMappings={importState.mappings}
            onMappingComplete={handleMappingComplete}
            onGoBack={handleGoBack}
          />
        );
      case 'preview':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ImportPreviewStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            onStartImport={handleStartImport}
            onGoBack={handleGoBack}
          />
        );
      case 'importing':
      case 'complete':
        if (!importState.excelData || !importState.mappings.length) return null;
        return (
          <ImportProgressStep
            excelData={importState.excelData}
            mappings={importState.mappings}
            isComplete={importState.step === 'complete'}
            onImportComplete={handleImportComplete}
            onClose={handleClose}
          />
        );
      default:
        return null;
    }
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      {
        id: 'upload',
        title: t('customer.import.steps.upload'),
        icon: 'upload'
      },
      {
        id: 'mapping',
        title: t('customer.import.steps.mapping'),
        icon: 'link'
      },
      {
        id: 'preview',
        title: t('customer.import.steps.preview'),
        icon: 'search'
      },
      {
        id: 'importing',
        title: t('customer.import.steps.importing'),
        icon: 'database'
      },
    ];

    const currentStepIndex = steps.findIndex(step => step.id === importState.step);

    return (
      <Stepper
        steps={steps}
        currentStep={currentStepIndex}
        variant="filled"
        size="lg"
        colorScheme="primary"
        showStepIcons={true}
        showConnector={true}
        orientation="horizontal"
        responsive={true}
        animated={true}
        className="w-full"
      />
    );
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Card className="w-full shadow-md border-0">
        {/* Header */}
        <div className="mb-6">
          <Typography variant="h4">
            {t('customer.import.title')}
          </Typography>
        </div>

        {/* Step Indicator */}
        <div className="mb-6">
          {renderStepIndicator()}
        </div>

        {/* Content */}
        <div className="overflow-y-auto mb-6">
          {renderStepContent()}
        </div>

        {/* Footer with Close Button */}
        <div className="flex justify-end pt-4 border-t border-border">
          <IconCard
            icon="x"
            title={t('common.close')}
            variant="primary"
            onClick={handleClose}
          />
        </div>
      </Card>
    </div>
  );
};

export default CustomerImport;
